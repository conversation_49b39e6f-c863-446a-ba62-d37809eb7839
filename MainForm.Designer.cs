namespace PPTPiliangChuli
{
    partial class MainForm
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MainForm));
            menuStrip1 = new MenuStrip();
            文件ToolStripMenuItem = new ToolStripMenuItem();
            打开源目录ToolStripMenuItem = new ToolStripMenuItem();
            打开输出目录ToolStripMenuItem = new ToolStripMenuItem();
            打开日志目录ToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator1 = new ToolStripSeparator();
            退出ToolStripMenuItem = new ToolStripMenuItem();
            操作ToolStripMenuItem = new ToolStripMenuItem();
            开始处理ToolStripMenuItem = new ToolStripMenuItem();
            停止处理ToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator2 = new ToolStripSeparator();
            导出配置ToolStripMenuItem = new ToolStripMenuItem();
            导入配置ToolStripMenuItem = new ToolStripMenuItem();
            重置配置ToolStripMenuItem = new ToolStripMenuItem();
            设置ToolStripMenuItem = new ToolStripMenuItem();
            日志设置ToolStripMenuItem = new ToolStripMenuItem();
            定时设置ToolStripMenuItem = new ToolStripMenuItem();
            帮助ToolStripMenuItem = new ToolStripMenuItem();
            使用帮助ToolStripMenuItem = new ToolStripMenuItem();
            快捷键说明ToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator6 = new ToolStripSeparator();
            配置诊断ToolStripMenuItem = new ToolStripMenuItem();
            检查更新ToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator7 = new ToolStripSeparator();
            关于软件ToolStripMenuItem = new ToolStripMenuItem();
            groupBoxPaths = new GroupBox();
            btnBrowseOutput = new Button();
            btnBrowseSource = new Button();
            chkKeepStructure = new CheckBox();
            chkIncludeSubfolders = new CheckBox();
            txtOutputPath = new TextBox();
            txtSourcePath = new TextBox();
            lblOutputPath = new Label();
            lblSourcePath = new Label();
            groupBoxSettings = new GroupBox();
            btnSupportedFormats = new Button();
            lblThreadCount = new Label();
            numThreadCount = new NumericUpDown();
            lblRetryCount = new Label();
            numRetryCount = new NumericUpDown();
            lblBatchSize = new Label();
            numBatchSize = new NumericUpDown();
            lblConflictHandling = new Label();
            lblConflictHandlingText = new Label();
            cmbConflictHandling = new ComboBox();
            radioCopy = new RadioButton();
            radioMove = new RadioButton();
            chkDeleteSource = new CheckBox();
            btnSelectAll = new Button();
            btnDeselectAll = new Button();
            groupBoxFunctions = new GroupBox();
            tableLayoutPanelFunctions = new TableLayoutPanel();
            groupBoxActions = new GroupBox();
            tableLayoutPanelActions = new TableLayoutPanel();
            groupBoxStats = new GroupBox();
            progressBar = new ProgressBar();
            lblStats = new Label();
            lblProgress = new Label();
            menuStrip1.SuspendLayout();
            groupBoxPaths.SuspendLayout();
            groupBoxSettings.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)numThreadCount).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numRetryCount).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numBatchSize).BeginInit();
            groupBoxFunctions.SuspendLayout();
            groupBoxActions.SuspendLayout();
            groupBoxStats.SuspendLayout();
            SuspendLayout();
            // 
            // menuStrip1
            // 
            menuStrip1.ImageScalingSize = new Size(24, 24);
            menuStrip1.Items.AddRange(new ToolStripItem[] { 文件ToolStripMenuItem, 操作ToolStripMenuItem, 设置ToolStripMenuItem, 帮助ToolStripMenuItem });
            menuStrip1.Location = new Point(0, 0);
            menuStrip1.Name = "menuStrip1";
            menuStrip1.Padding = new Padding(9, 3, 0, 3);
            menuStrip1.Size = new Size(1609, 34);
            menuStrip1.TabIndex = 0;
            menuStrip1.Text = "menuStrip1";
            // 
            // 文件ToolStripMenuItem
            //
            文件ToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { 打开源目录ToolStripMenuItem, 打开输出目录ToolStripMenuItem, 打开日志目录ToolStripMenuItem, toolStripSeparator1, 退出ToolStripMenuItem });
            文件ToolStripMenuItem.Name = "文件ToolStripMenuItem";
            文件ToolStripMenuItem.Size = new Size(62, 28);
            文件ToolStripMenuItem.Text = "文件";
            //
            // 打开源目录ToolStripMenuItem
            //
            打开源目录ToolStripMenuItem.Name = "打开源目录ToolStripMenuItem";
            打开源目录ToolStripMenuItem.Size = new Size(278, 34);
            打开源目录ToolStripMenuItem.Text = "打开源目录(&S)";
            打开源目录ToolStripMenuItem.Click += 打开源目录_Click;
            //
            // 打开输出目录ToolStripMenuItem
            //
            打开输出目录ToolStripMenuItem.Name = "打开输出目录ToolStripMenuItem";
            打开输出目录ToolStripMenuItem.Size = new Size(278, 34);
            打开输出目录ToolStripMenuItem.Text = "打开输出目录(&O)";
            打开输出目录ToolStripMenuItem.Click += 打开输出目录_Click;
            //
            // 打开日志目录ToolStripMenuItem
            //
            打开日志目录ToolStripMenuItem.Name = "打开日志目录ToolStripMenuItem";
            打开日志目录ToolStripMenuItem.Size = new Size(278, 34);
            打开日志目录ToolStripMenuItem.Text = "打开日志目录(&L)";
            打开日志目录ToolStripMenuItem.Click += 打开日志目录_Click;
            // 
            // toolStripSeparator1
            // 
            toolStripSeparator1.Name = "toolStripSeparator1";
            toolStripSeparator1.Size = new Size(275, 6);
            // 
            // 退出ToolStripMenuItem
            // 
            退出ToolStripMenuItem.Name = "退出ToolStripMenuItem";
            退出ToolStripMenuItem.ShortcutKeys = Keys.Alt | Keys.F4;
            退出ToolStripMenuItem.Size = new Size(278, 34);
            退出ToolStripMenuItem.Text = "退出(&X)";
            退出ToolStripMenuItem.Click += 退出_Click;
            // 
            // 操作ToolStripMenuItem
            //
            操作ToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { 开始处理ToolStripMenuItem, 停止处理ToolStripMenuItem, toolStripSeparator2, 导出配置ToolStripMenuItem, 导入配置ToolStripMenuItem, 重置配置ToolStripMenuItem });
            操作ToolStripMenuItem.Name = "操作ToolStripMenuItem";
            操作ToolStripMenuItem.Size = new Size(62, 28);
            操作ToolStripMenuItem.Text = "操作";
            // 
            // 开始处理ToolStripMenuItem
            // 
            开始处理ToolStripMenuItem.Name = "开始处理ToolStripMenuItem";
            开始处理ToolStripMenuItem.ShortcutKeys = Keys.F5;
            开始处理ToolStripMenuItem.Size = new Size(279, 34);
            开始处理ToolStripMenuItem.Text = "开始处理(&S)";
            开始处理ToolStripMenuItem.Click += 开始处理_Click;
            // 
            // 停止处理ToolStripMenuItem
            // 
            停止处理ToolStripMenuItem.Enabled = false;
            停止处理ToolStripMenuItem.Name = "停止处理ToolStripMenuItem";
            停止处理ToolStripMenuItem.ShortcutKeys = Keys.Control | Keys.F5;
            停止处理ToolStripMenuItem.Size = new Size(279, 34);
            停止处理ToolStripMenuItem.Text = "停止处理(&T)";
            停止处理ToolStripMenuItem.Click += 停止处理_Click;
            //
            // toolStripSeparator2
            // 
            toolStripSeparator2.Name = "toolStripSeparator2";
            toolStripSeparator2.Size = new Size(276, 6);
            //
            // 导出配置ToolStripMenuItem
            //
            导出配置ToolStripMenuItem.Name = "导出配置ToolStripMenuItem";
            导出配置ToolStripMenuItem.Size = new Size(279, 34);
            导出配置ToolStripMenuItem.Text = "导出配置(&E)";
            导出配置ToolStripMenuItem.Click += 导出配置_Click;
            //
            // 导入配置ToolStripMenuItem
            //
            导入配置ToolStripMenuItem.Name = "导入配置ToolStripMenuItem";
            导入配置ToolStripMenuItem.Size = new Size(279, 34);
            导入配置ToolStripMenuItem.Text = "导入配置(&I)";
            导入配置ToolStripMenuItem.Click += 导入配置_Click;
            //
            // 重置配置ToolStripMenuItem
            //
            重置配置ToolStripMenuItem.Name = "重置配置ToolStripMenuItem";
            重置配置ToolStripMenuItem.Size = new Size(279, 34);
            重置配置ToolStripMenuItem.Text = "重置配置(&R)";
            重置配置ToolStripMenuItem.Click += 重置配置_Click;
            //
            // 设置ToolStripMenuItem
            //
            设置ToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { 日志设置ToolStripMenuItem, 定时设置ToolStripMenuItem });
            设置ToolStripMenuItem.Name = "设置ToolStripMenuItem";
            设置ToolStripMenuItem.Size = new Size(62, 28);
            设置ToolStripMenuItem.Text = "设置";
            // 
            // 日志设置ToolStripMenuItem
            // 
            日志设置ToolStripMenuItem.Name = "日志设置ToolStripMenuItem";
            日志设置ToolStripMenuItem.Size = new Size(242, 34);
            日志设置ToolStripMenuItem.Text = "日志设置(&L)";
            日志设置ToolStripMenuItem.Click += 日志设置_Click;
            //
            // 定时设置ToolStripMenuItem
            //
            定时设置ToolStripMenuItem.Name = "定时设置ToolStripMenuItem";
            定时设置ToolStripMenuItem.Size = new Size(242, 34);
            定时设置ToolStripMenuItem.Text = "定时设置(&T)";
            定时设置ToolStripMenuItem.Click += 定时设置_Click;
            // 
            // 帮助ToolStripMenuItem
            // 
            帮助ToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { 使用帮助ToolStripMenuItem, 快捷键说明ToolStripMenuItem, toolStripSeparator6, 配置诊断ToolStripMenuItem, 检查更新ToolStripMenuItem, toolStripSeparator7, 关于软件ToolStripMenuItem });
            帮助ToolStripMenuItem.Name = "帮助ToolStripMenuItem";
            帮助ToolStripMenuItem.Size = new Size(62, 28);
            帮助ToolStripMenuItem.Text = "帮助";
            // 
            // 使用帮助ToolStripMenuItem
            // 
            使用帮助ToolStripMenuItem.Name = "使用帮助ToolStripMenuItem";
            使用帮助ToolStripMenuItem.ShortcutKeys = Keys.F1;
            使用帮助ToolStripMenuItem.Size = new Size(239, 34);
            使用帮助ToolStripMenuItem.Text = "使用帮助(&H)";
            使用帮助ToolStripMenuItem.Click += 使用帮助_Click;
            // 
            // 快捷键说明ToolStripMenuItem
            // 
            快捷键说明ToolStripMenuItem.Name = "快捷键说明ToolStripMenuItem";
            快捷键说明ToolStripMenuItem.Size = new Size(239, 34);
            快捷键说明ToolStripMenuItem.Text = "快捷键说明(&K)";
            快捷键说明ToolStripMenuItem.Click += 快捷键说明_Click;
            // 
            // toolStripSeparator6
            //
            toolStripSeparator6.Name = "toolStripSeparator6";
            toolStripSeparator6.Size = new Size(236, 6);
            //
            // 配置诊断ToolStripMenuItem
            //
            配置诊断ToolStripMenuItem.Name = "配置诊断ToolStripMenuItem";
            配置诊断ToolStripMenuItem.Size = new Size(239, 34);
            配置诊断ToolStripMenuItem.Text = "配置诊断(&D)";
            配置诊断ToolStripMenuItem.Click += 配置诊断_Click;
            //
            // 检查更新ToolStripMenuItem
            // 
            检查更新ToolStripMenuItem.Name = "检查更新ToolStripMenuItem";
            检查更新ToolStripMenuItem.Size = new Size(239, 34);
            检查更新ToolStripMenuItem.Text = "检查更新(&U)";
            检查更新ToolStripMenuItem.Click += 检查更新_Click;
            // 
            // toolStripSeparator7
            // 
            toolStripSeparator7.Name = "toolStripSeparator7";
            toolStripSeparator7.Size = new Size(236, 6);
            //
            // 关于软件ToolStripMenuItem
            //
            关于软件ToolStripMenuItem.Name = "关于软件ToolStripMenuItem";
            关于软件ToolStripMenuItem.Size = new Size(239, 34);
            关于软件ToolStripMenuItem.Text = "关于软件(&A)";
            关于软件ToolStripMenuItem.Click += 关于软件_Click;
            // 
            // groupBoxPaths
            // 
            groupBoxPaths.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxPaths.Controls.Add(btnBrowseOutput);
            groupBoxPaths.Controls.Add(btnBrowseSource);
            groupBoxPaths.Controls.Add(chkKeepStructure);
            groupBoxPaths.Controls.Add(chkIncludeSubfolders);
            groupBoxPaths.Controls.Add(txtOutputPath);
            groupBoxPaths.Controls.Add(txtSourcePath);
            groupBoxPaths.Controls.Add(lblOutputPath);
            groupBoxPaths.Controls.Add(lblSourcePath);
            groupBoxPaths.Location = new Point(19, 43);
            groupBoxPaths.Margin = new Padding(5);
            groupBoxPaths.Name = "groupBoxPaths";
            groupBoxPaths.Padding = new Padding(5);
            groupBoxPaths.Size = new Size(1571, 128);
            groupBoxPaths.TabIndex = 1;
            groupBoxPaths.TabStop = false;
            groupBoxPaths.Text = "路径设置";
            // 
            // btnBrowseOutput
            // 
            btnBrowseOutput.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnBrowseOutput.Location = new Point(1460, 77);
            btnBrowseOutput.Margin = new Padding(5);
            btnBrowseOutput.Name = "btnBrowseOutput";
            btnBrowseOutput.Size = new Size(95, 40);
            btnBrowseOutput.TabIndex = 7;
            btnBrowseOutput.Text = "浏览";
            btnBrowseOutput.UseVisualStyleBackColor = true;
            // 
            // btnBrowseSource
            // 
            btnBrowseSource.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnBrowseSource.Location = new Point(1460, 30);
            btnBrowseSource.Margin = new Padding(5);
            btnBrowseSource.Name = "btnBrowseSource";
            btnBrowseSource.Size = new Size(95, 40);
            btnBrowseSource.TabIndex = 6;
            btnBrowseSource.Text = "浏览";
            btnBrowseSource.UseVisualStyleBackColor = true;
            // 
            // chkKeepStructure
            // 
            chkKeepStructure.AutoSize = true;
            chkKeepStructure.Location = new Point(1304, 80);
            chkKeepStructure.Margin = new Padding(5);
            chkKeepStructure.Name = "chkKeepStructure";
            chkKeepStructure.Size = new Size(144, 28);
            chkKeepStructure.TabIndex = 5;
            chkKeepStructure.Text = "保持目录结构";
            chkKeepStructure.UseVisualStyleBackColor = true;
            // 
            // chkIncludeSubfolders
            // 
            chkIncludeSubfolders.AutoSize = true;
            chkIncludeSubfolders.Location = new Point(1304, 34);
            chkIncludeSubfolders.Margin = new Padding(5);
            chkIncludeSubfolders.Name = "chkIncludeSubfolders";
            chkIncludeSubfolders.Size = new Size(126, 28);
            chkIncludeSubfolders.TabIndex = 4;
            chkIncludeSubfolders.Text = "包含子目录";
            chkIncludeSubfolders.UseVisualStyleBackColor = true;
            // 
            // txtOutputPath
            // 
            txtOutputPath.Location = new Point(176, 77);
            txtOutputPath.Margin = new Padding(5);
            txtOutputPath.Name = "txtOutputPath";
            txtOutputPath.Size = new Size(1117, 30);
            txtOutputPath.TabIndex = 3;
            // 
            // txtSourcePath
            // 
            txtSourcePath.Location = new Point(176, 30);
            txtSourcePath.Margin = new Padding(5);
            txtSourcePath.Name = "txtSourcePath";
            txtSourcePath.Size = new Size(1117, 30);
            txtSourcePath.TabIndex = 2;
            // 
            // lblOutputPath
            // 
            lblOutputPath.AutoSize = true;
            lblOutputPath.Location = new Point(9, 82);
            lblOutputPath.Margin = new Padding(5, 0, 5, 0);
            lblOutputPath.Name = "lblOutputPath";
            lblOutputPath.Size = new Size(100, 24);
            lblOutputPath.TabIndex = 1;
            lblOutputPath.Text = "输出目录：";
            // 
            // lblSourcePath
            // 
            lblSourcePath.AutoSize = true;
            lblSourcePath.Location = new Point(9, 35);
            lblSourcePath.Margin = new Padding(5, 0, 5, 0);
            lblSourcePath.Name = "lblSourcePath";
            lblSourcePath.Size = new Size(100, 24);
            lblSourcePath.TabIndex = 0;
            lblSourcePath.Text = "来源目录：";
            // 
            // groupBoxSettings
            // 
            groupBoxSettings.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxSettings.Controls.Add(btnSupportedFormats);
            groupBoxSettings.Controls.Add(lblThreadCount);
            groupBoxSettings.Controls.Add(numThreadCount);
            groupBoxSettings.Controls.Add(lblRetryCount);
            groupBoxSettings.Controls.Add(numRetryCount);
            groupBoxSettings.Controls.Add(lblBatchSize);
            groupBoxSettings.Controls.Add(numBatchSize);
            groupBoxSettings.Controls.Add(lblConflictHandling);
            groupBoxSettings.Controls.Add(lblConflictHandlingText);
            groupBoxSettings.Controls.Add(cmbConflictHandling);
            groupBoxSettings.Controls.Add(radioCopy);
            groupBoxSettings.Controls.Add(radioMove);
            groupBoxSettings.Controls.Add(chkDeleteSource);
            groupBoxSettings.Controls.Add(btnSelectAll);
            groupBoxSettings.Controls.Add(btnDeselectAll);
            groupBoxSettings.Location = new Point(19, 181);
            groupBoxSettings.Margin = new Padding(5);
            groupBoxSettings.Name = "groupBoxSettings";
            groupBoxSettings.Padding = new Padding(5);
            groupBoxSettings.Size = new Size(1571, 160);
            groupBoxSettings.TabIndex = 2;
            groupBoxSettings.TabStop = false;
            groupBoxSettings.Text = "处理设置";
            // 
            // btnSupportedFormats
            // 
            btnSupportedFormats.Location = new Point(16, 40);
            btnSupportedFormats.Margin = new Padding(5);
            btnSupportedFormats.Name = "btnSupportedFormats";
            btnSupportedFormats.Size = new Size(157, 40);
            btnSupportedFormats.TabIndex = 12;
            btnSupportedFormats.Text = "支持格式设定";
            btnSupportedFormats.UseVisualStyleBackColor = true;
            // 
            // lblThreadCount
            // 
            lblThreadCount.AutoSize = true;
            lblThreadCount.Location = new Point(204, 48);
            lblThreadCount.Margin = new Padding(5, 0, 5, 0);
            lblThreadCount.Name = "lblThreadCount";
            lblThreadCount.Size = new Size(68, 24);
            lblThreadCount.TabIndex = 4;
            lblThreadCount.Text = "线程数:";
            // 
            // numThreadCount
            // 
            numThreadCount.Location = new Point(299, 43);
            numThreadCount.Margin = new Padding(5);
            numThreadCount.Maximum = new decimal(new int[] { 16, 0, 0, 0 });
            numThreadCount.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            numThreadCount.Name = "numThreadCount";
            numThreadCount.Size = new Size(94, 30);
            numThreadCount.TabIndex = 5;
            numThreadCount.Value = new decimal(new int[] { 1, 0, 0, 0 });
            // 
            // lblRetryCount
            // 
            lblRetryCount.AutoSize = true;
            lblRetryCount.Location = new Point(424, 48);
            lblRetryCount.Margin = new Padding(5, 0, 5, 0);
            lblRetryCount.Name = "lblRetryCount";
            lblRetryCount.Size = new Size(86, 24);
            lblRetryCount.TabIndex = 6;
            lblRetryCount.Text = "重试次数:";
            // 
            // numRetryCount
            // 
            numRetryCount.Location = new Point(541, 43);
            numRetryCount.Margin = new Padding(5);
            numRetryCount.Maximum = new decimal(new int[] { 10, 0, 0, 0 });
            numRetryCount.Name = "numRetryCount";
            numRetryCount.Size = new Size(94, 30);
            numRetryCount.TabIndex = 7;
            numRetryCount.Value = new decimal(new int[] { 3, 0, 0, 0 });
            // 
            // lblBatchSize
            // 
            lblBatchSize.AutoSize = true;
            lblBatchSize.Location = new Point(666, 48);
            lblBatchSize.Margin = new Padding(5, 0, 5, 0);
            lblBatchSize.Name = "lblBatchSize";
            lblBatchSize.Size = new Size(104, 24);
            lblBatchSize.TabIndex = 8;
            lblBatchSize.Text = "批处理数量:";
            // 
            // numBatchSize
            // 
            numBatchSize.Location = new Point(801, 43);
            numBatchSize.Margin = new Padding(5);
            numBatchSize.Maximum = new decimal(new int[] { 1000, 0, 0, 0 });
            numBatchSize.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            numBatchSize.Name = "numBatchSize";
            numBatchSize.Size = new Size(94, 30);
            numBatchSize.TabIndex = 9;
            numBatchSize.Value = new decimal(new int[] { 50, 0, 0, 0 });
            // 
            // lblConflictHandling
            // 
            lblConflictHandling.AutoSize = true;
            lblConflictHandling.Location = new Point(16, 104);
            lblConflictHandling.Margin = new Padding(5, 0, 5, 0);
            lblConflictHandling.Name = "lblConflictHandling";
            lblConflictHandling.Size = new Size(86, 24);
            lblConflictHandling.TabIndex = 0;
            lblConflictHandling.Text = "移动模式:";
            // 
            // lblConflictHandlingText
            // 
            lblConflictHandlingText.AutoSize = true;
            lblConflictHandlingText.Location = new Point(519, 102);
            lblConflictHandlingText.Margin = new Padding(5, 0, 5, 0);
            lblConflictHandlingText.Name = "lblConflictHandlingText";
            lblConflictHandlingText.Size = new Size(100, 24);
            lblConflictHandlingText.TabIndex = 12;
            lblConflictHandlingText.Text = "冲突处理：";
            lblConflictHandlingText.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // cmbConflictHandling
            // 
            cmbConflictHandling.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbConflictHandling.FormattingEnabled = true;
            cmbConflictHandling.Items.AddRange(new object[] { "自动重命名", "覆盖现有文件", "跳过冲突文件", "询问用户" });
            cmbConflictHandling.Location = new Point(621, 99);
            cmbConflictHandling.Margin = new Padding(5);
            cmbConflictHandling.Name = "cmbConflictHandling";
            cmbConflictHandling.Size = new Size(186, 40);
            cmbConflictHandling.TabIndex = 4;
            // 
            // radioCopy
            // 
            radioCopy.AutoSize = true;
            radioCopy.Checked = true;
            radioCopy.Location = new Point(132, 101);
            radioCopy.Margin = new Padding(5);
            radioCopy.Name = "radioCopy";
            radioCopy.Size = new Size(71, 28);
            radioCopy.TabIndex = 1;
            radioCopy.TabStop = true;
            radioCopy.Text = "复制";
            radioCopy.UseVisualStyleBackColor = true;
            // 
            // radioMove
            // 
            radioMove.AutoSize = true;
            radioMove.Location = new Point(220, 101);
            radioMove.Margin = new Padding(5);
            radioMove.Name = "radioMove";
            radioMove.Size = new Size(71, 28);
            radioMove.TabIndex = 2;
            radioMove.Text = "移动";
            radioMove.UseVisualStyleBackColor = true;
            // 
            // chkDeleteSource
            // 
            chkDeleteSource.AutoSize = true;
            chkDeleteSource.Location = new Point(330, 101);
            chkDeleteSource.Margin = new Padding(5);
            chkDeleteSource.Name = "chkDeleteSource";
            chkDeleteSource.Size = new Size(162, 28);
            chkDeleteSource.TabIndex = 3;
            chkDeleteSource.Text = "直接处理源文件";
            chkDeleteSource.UseVisualStyleBackColor = true;
            // 
            // btnSelectAll
            // 
            btnSelectAll.Location = new Point(849, 96);
            btnSelectAll.Margin = new Padding(5);
            btnSelectAll.Name = "btnSelectAll";
            btnSelectAll.Size = new Size(110, 40);
            btnSelectAll.TabIndex = 11;
            btnSelectAll.Text = "全选";
            btnSelectAll.UseVisualStyleBackColor = true;
            // 
            // btnDeselectAll
            // 
            btnDeselectAll.Location = new Point(966, 96);
            btnDeselectAll.Margin = new Padding(5);
            btnDeselectAll.Name = "btnDeselectAll";
            btnDeselectAll.Size = new Size(110, 40);
            btnDeselectAll.TabIndex = 10;
            btnDeselectAll.Text = "取消全选";
            btnDeselectAll.UseVisualStyleBackColor = true;
            // 
            // groupBoxFunctions
            // 
            groupBoxFunctions.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxFunctions.Controls.Add(tableLayoutPanelFunctions);
            groupBoxFunctions.Location = new Point(19, 350);
            groupBoxFunctions.Margin = new Padding(5);
            groupBoxFunctions.Name = "groupBoxFunctions";
            groupBoxFunctions.Padding = new Padding(5);
            groupBoxFunctions.Size = new Size(1571, 320);
            groupBoxFunctions.TabIndex = 3;
            groupBoxFunctions.TabStop = false;
            groupBoxFunctions.Text = "功能选择";
            // 
            // tableLayoutPanelFunctions
            // 
            tableLayoutPanelFunctions.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            tableLayoutPanelFunctions.ColumnCount = 3;
            tableLayoutPanelFunctions.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33333F));
            tableLayoutPanelFunctions.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33333F));
            tableLayoutPanelFunctions.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33333F));
            tableLayoutPanelFunctions.Location = new Point(9, 35);
            tableLayoutPanelFunctions.Margin = new Padding(5);
            tableLayoutPanelFunctions.Name = "tableLayoutPanelFunctions";
            tableLayoutPanelFunctions.RowCount = 3;
            tableLayoutPanelFunctions.RowStyles.Add(new RowStyle(SizeType.Percent, 33.33333F));
            tableLayoutPanelFunctions.RowStyles.Add(new RowStyle(SizeType.Percent, 33.33333F));
            tableLayoutPanelFunctions.RowStyles.Add(new RowStyle(SizeType.Percent, 33.33333F));
            tableLayoutPanelFunctions.Size = new Size(1553, 275);
            tableLayoutPanelFunctions.TabIndex = 0;
            // 
            // groupBoxActions
            // 
            groupBoxActions.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxActions.Controls.Add(tableLayoutPanelActions);
            groupBoxActions.Location = new Point(19, 680);
            groupBoxActions.Margin = new Padding(5);
            groupBoxActions.Name = "groupBoxActions";
            groupBoxActions.Padding = new Padding(5);
            groupBoxActions.Size = new Size(1571, 280);
            groupBoxActions.TabIndex = 4;
            groupBoxActions.TabStop = false;
            groupBoxActions.Text = "操作控制";
            // 
            // tableLayoutPanelActions
            // 
            tableLayoutPanelActions.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            tableLayoutPanelActions.ColumnCount = 3;
            tableLayoutPanelActions.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33333F));
            tableLayoutPanelActions.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33333F));
            tableLayoutPanelActions.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33333F));
            tableLayoutPanelActions.Location = new Point(9, 35);
            tableLayoutPanelActions.Margin = new Padding(5);
            tableLayoutPanelActions.Name = "tableLayoutPanelActions";
            tableLayoutPanelActions.RowCount = 3;
            tableLayoutPanelActions.RowStyles.Add(new RowStyle(SizeType.Percent, 33.33333F));
            tableLayoutPanelActions.RowStyles.Add(new RowStyle(SizeType.Percent, 33.33333F));
            tableLayoutPanelActions.RowStyles.Add(new RowStyle(SizeType.Percent, 33.33333F));
            tableLayoutPanelActions.Size = new Size(1553, 235);
            tableLayoutPanelActions.TabIndex = 0;
            // 
            // groupBoxStats
            // 
            groupBoxStats.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxStats.Controls.Add(progressBar);
            groupBoxStats.Controls.Add(lblStats);
            groupBoxStats.Controls.Add(lblProgress);
            groupBoxStats.Location = new Point(19, 965);
            groupBoxStats.Margin = new Padding(5);
            groupBoxStats.Name = "groupBoxStats";
            groupBoxStats.Padding = new Padding(5);
            groupBoxStats.Size = new Size(1571, 160);
            groupBoxStats.TabIndex = 5;
            groupBoxStats.TabStop = false;
            groupBoxStats.Text = "处理统计";
            // 
            // progressBar
            // 
            progressBar.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            progressBar.Location = new Point(9, 82);
            progressBar.Margin = new Padding(5);
            progressBar.Name = "progressBar";
            progressBar.Size = new Size(1553, 37);
            progressBar.TabIndex = 2;
            // 
            // lblStats
            // 
            lblStats.AutoSize = true;
            lblStats.Location = new Point(9, 35);
            lblStats.Margin = new Padding(5, 0, 5, 15);
            lblStats.Name = "lblStats";
            lblStats.Size = new Size(674, 24);
            lblStats.TabIndex = 1;
            lblStats.Text = "总文件数: 0  成功处理: 0 (0.0%)  处理失败: 0 (0.0%)  开始时间: --  预计结束时间: --";
            // 
            // lblProgress
            // 
            lblProgress.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            lblProgress.Location = new Point(9, 128);
            lblProgress.Margin = new Padding(5, 0, 5, 0);
            lblProgress.Name = "lblProgress";
            lblProgress.Size = new Size(1553, 24);
            lblProgress.TabIndex = 0;
            lblProgress.Text = "处理速度: 0 文件/分钟    处理耗时: 00:00:00";
            // 
            // MainForm
            // 
            AutoScaleDimensions = new SizeF(11F, 24F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1609, 1200);
            Controls.Add(groupBoxStats);
            Controls.Add(groupBoxActions);
            Controls.Add(groupBoxFunctions);
            Controls.Add(groupBoxSettings);
            Controls.Add(groupBoxPaths);
            Controls.Add(menuStrip1);
            Icon = (Icon)resources.GetObject("$this.Icon");
            MainMenuStrip = menuStrip1;
            Margin = new Padding(5);
            MinimumSize = new Size(1622, 1200);
            Name = "MainForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "PPT批量处理工具";
            menuStrip1.ResumeLayout(false);
            menuStrip1.PerformLayout();
            groupBoxPaths.ResumeLayout(false);
            groupBoxPaths.PerformLayout();
            groupBoxSettings.ResumeLayout(false);
            groupBoxSettings.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)numThreadCount).EndInit();
            ((System.ComponentModel.ISupportInitialize)numRetryCount).EndInit();
            ((System.ComponentModel.ISupportInitialize)numBatchSize).EndInit();
            groupBoxFunctions.ResumeLayout(false);
            groupBoxActions.ResumeLayout(false);
            groupBoxStats.ResumeLayout(false);
            groupBoxStats.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private MenuStrip menuStrip1;
        private ToolStripMenuItem 文件ToolStripMenuItem;
        private ToolStripMenuItem 打开源目录ToolStripMenuItem;
        private ToolStripMenuItem 打开输出目录ToolStripMenuItem;
        private ToolStripMenuItem 打开日志目录ToolStripMenuItem;
        private ToolStripSeparator toolStripSeparator1;
        private ToolStripMenuItem 退出ToolStripMenuItem;
        private ToolStripMenuItem 操作ToolStripMenuItem;
        private ToolStripMenuItem 开始处理ToolStripMenuItem;
        private ToolStripMenuItem 停止处理ToolStripMenuItem;
        private ToolStripSeparator toolStripSeparator2;
        private ToolStripMenuItem 导出配置ToolStripMenuItem;
        private ToolStripMenuItem 导入配置ToolStripMenuItem;
        private ToolStripMenuItem 重置配置ToolStripMenuItem;
        private ToolStripMenuItem 设置ToolStripMenuItem;
        private ToolStripMenuItem 日志设置ToolStripMenuItem;
        private ToolStripMenuItem 定时设置ToolStripMenuItem;
        private ToolStripMenuItem 帮助ToolStripMenuItem;
        private ToolStripMenuItem 使用帮助ToolStripMenuItem;
        private ToolStripMenuItem 快捷键说明ToolStripMenuItem;
        private ToolStripSeparator toolStripSeparator6;
        private ToolStripMenuItem 配置诊断ToolStripMenuItem;
        private ToolStripMenuItem 检查更新ToolStripMenuItem;
        private ToolStripSeparator toolStripSeparator7;
        private ToolStripMenuItem 关于软件ToolStripMenuItem;
        private GroupBox groupBoxPaths;
        private Button btnBrowseOutput;
        private Button btnBrowseSource;
        private CheckBox chkKeepStructure;
        private CheckBox chkIncludeSubfolders;
        private TextBox txtOutputPath;
        private TextBox txtSourcePath;
        private Label lblOutputPath;
        private Label lblSourcePath;
        private GroupBox groupBoxSettings;
        private Button btnSelectAll;
        private Button btnDeselectAll;
        private NumericUpDown numBatchSize;
        private Label lblBatchSize;
        private NumericUpDown numRetryCount;
        private Label lblRetryCount;
        private NumericUpDown numThreadCount;
        private Label lblThreadCount;
        private CheckBox chkDeleteSource;
        private RadioButton radioMove;
        private RadioButton radioCopy;
        private Label lblConflictHandling;
        private Label lblConflictHandlingText;
        private ComboBox cmbConflictHandling;
        private Button btnSupportedFormats;
        private GroupBox groupBoxFunctions;
        private TableLayoutPanel tableLayoutPanelFunctions;
        private GroupBox groupBoxActions;
        private TableLayoutPanel tableLayoutPanelActions;
        private GroupBox groupBoxStats;
        private ProgressBar progressBar;
        private Label lblStats;
        private Label lblProgress;
    }
}
